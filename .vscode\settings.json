{"python.analysis.autoImportCompletions": true, "C_Cpp.default.compilerPath": "cl.exe", "C_Cpp.default.intelliSenseMode": "windows-msvc-x64", "C_Cpp.clang_format_path": "clang-format.exe", "C_Cpp.codeAnalysis.clangTidy.enabled": true, "C_Cpp.codeAnalysis.clangTidy.path": "clang-tidy.exe", "C_Cpp.default.includePath": ["${workspaceFolder}/client/include", "${workspaceFolder}/client/src"], "cmake.ignoreCMakeListsMissing": true, "github.copilot.nextEditSuggestions.enabled": true, "files.associations": {"memory": "cpp"}}