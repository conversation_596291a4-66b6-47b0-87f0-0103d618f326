{"version": "2.0.0", "tasks": [{"label": "Clean Build", "type": "shell", "command": "cmd", "args": ["/c", "del /q client\\EncryptedBackupClient.exe 2>nul & del /q build\\client\\*.obj 2>nul & del /q build\\crypto++\\*.obj 2>nul & echo Cleaned build artifacts"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Run Client", "type": "shell", "command": "cmd", "args": ["/c", "cd client && .\\EncryptedBackupClient.exe"], "group": "test", "dependsOn": "Build with MSVC", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"type": "cppbuild", "label": "C/C++: clang-cl.exe build active file", "command": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\Llvm\\x64\\bin\\clang-cl.exe", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\Llvm\\x64\\bin"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}]}