// ClientGUI.cpp - Windows Native GUI Implementation for Encrypted Backup Client
// Implements Option 4 (Simple Popup + System Tray) with Option 1 (Windows Native API)

#ifdef _WIN32

#include "ClientGUI.h"
#include <windows.h>
#include <shellapi.h>
#include <commctrl.h>
#include <windowsx.h>
#include <sstream>
#include <iomanip>
#include <string>

// Static instance for singleton pattern
static std::unique_ptr<ClientGUI> g_clientGUI = nullptr;

// Window class names
static const wchar_t* STATUS_WINDOW_CLASS = L"EncryptedBackupStatusWindow";
static const wchar_t* TRAY_WINDOW_CLASS = L"EncryptedBackupTrayWindow";

// Constructor
ClientGUI::ClientGUI() 
    : statusWindow(nullptr)
    , consoleWindow(GetConsoleWindow())
    , guiEnabled(false)
    , statusWindowVisible(false)
    , shouldClose(false)
    , guiInitialized(false) 
{
    InitializeCriticalSection(&statusLock);
    ZeroMemory(&trayIcon, sizeof(trayIcon));
    ZeroMemory(&currentStatus, sizeof(currentStatus));
    
    // Initialize status with defaults
    currentStatus.phase = "Initializing";
    currentStatus.connected = false;
    currentStatus.progress = 0;
    currentStatus.totalProgress = 100;
}

// Destructor
ClientGUI::~ClientGUI() {
    shutdown();
    DeleteCriticalSection(&statusLock);
}

// Get singleton instance
ClientGUI* ClientGUI::getInstance() {
    if (!g_clientGUI) {
        g_clientGUI = std::make_unique<ClientGUI>();
    }
    return g_clientGUI.get();
}

// Initialize GUI system
bool ClientGUI::initialize() {
    if (guiInitialized.load()) {
        return true; // Already initialized
    }
    
    try {
        // Register window classes
        WNDCLASSEXW wc = {};
        wc.cbSize = sizeof(WNDCLASSEXW);
        wc.lpfnWndProc = StatusWindowProc;
        wc.hInstance = GetModuleHandle(nullptr);
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.lpszClassName = STATUS_WINDOW_CLASS;
        wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        
        if (!RegisterClassExW(&wc)) {
            return false;
        }
        
        // Register tray window class (hidden message-only window)
        wc.lpfnWndProc = TrayWindowProc;
        wc.lpszClassName = TRAY_WINDOW_CLASS;
        wc.hbrBackground = nullptr;
        
        if (!RegisterClassExW(&wc)) {
            return false;
        }
        
        // Start GUI thread
        guiThread = std::thread(&ClientGUI::guiMessageLoop, this);
        
        // Wait for initialization to complete
        int attempts = 0;
        while (!guiInitialized.load() && attempts < 50) {
            Sleep(100);
            attempts++;
        }
        
        return guiInitialized.load();
        
    } catch (...) {
        return false;
    }
}

// GUI message loop (runs in separate thread)
void ClientGUI::guiMessageLoop() {
    try {
        // Create message-only window for tray icon
        HWND trayWindow = CreateWindowExW(0, TRAY_WINDOW_CLASS, L"", 0, 0, 0, 0, 0, 
                                        HWND_MESSAGE, nullptr, GetModuleHandle(nullptr), this);
        
        if (!trayWindow) {
            return;
        }
        
        // Initialize tray icon
        if (!initializeTrayIcon()) {
            DestroyWindow(trayWindow);
            return;
        }
        
        // Create status window (initially hidden)
        if (!createStatusWindow()) {
            cleanup();
            DestroyWindow(trayWindow);
            return;
        }
        
        guiInitialized.store(true);
        
        // Message loop
        MSG msg;
        while (!shouldClose.load()) {
            BOOL result = GetMessage(&msg, nullptr, 0, 0);
            if (result == 0 || result == -1) { // WM_QUIT or error
                break;
            }
            
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        cleanup();
        DestroyWindow(trayWindow);
        
    } catch (...) {
        // Ensure cleanup on any exception
        cleanup();
    }
}

// Initialize system tray icon
bool ClientGUI::initializeTrayIcon() {
    ZeroMemory(&trayIcon, sizeof(trayIcon));
    
    trayIcon.cbSize = sizeof(trayIcon);
    trayIcon.hWnd = FindWindowW(TRAY_WINDOW_CLASS, nullptr);
    trayIcon.uID = 1;
    trayIcon.uFlags = NIF_ICON | NIF_MESSAGE | NIF_TIP | NIF_INFO;
    trayIcon.uCallbackMessage = WM_TRAYICON;
    
    // Load default icon
    trayIcon.hIcon = LoadIcon(GetModuleHandle(nullptr), IDI_APPLICATION);
    if (!trayIcon.hIcon) {
        trayIcon.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    }
    
    wchar_t szTip[128];
    wcscpy_s(szTip, L"Encrypted Backup Client");
    wcscpy_s(trayIcon.szTip, szTip);
    wchar_t szInfo[256];
    wcsncpy_s(szInfo, L"Client is initializing...", _TRUNCATE);
    wcscpy_s(trayIcon.szInfo, szInfo);
    wchar_t szInfoTitle[256];
    wcsncpy_s(szInfoTitle, L"Backup Client", _TRUNCATE);
    wcscpy_s(trayIcon.szInfoTitle, szInfoTitle);
    trayIcon.dwInfoFlags = NIIF_INFO;
    
    return Shell_NotifyIcon(NIM_ADD, &trayIcon) == TRUE;
}

// Create status window
bool ClientGUI::createStatusWindow() {
    statusWindow = CreateWindowExW(
        WS_EX_TOPMOST | WS_EX_TOOLWINDOW,
        STATUS_WINDOW_CLASS,
        L"Backup Client Status",
        WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU | WS_VISIBLE, // Added WS_VISIBLE
        CW_USEDEFAULT, CW_USEDEFAULT, 400, 300,
        nullptr, nullptr, GetModuleHandle(nullptr), this
    );
    
    if (statusWindow) {
        showStatusWindow(true); // Ensure window is shown
        // Center the window on the screen
        RECT rc;
        GetWindowRect(statusWindow, &rc);
        int winWidth = rc.right - rc.left;
        int winHeight = rc.bottom - rc.top;
        int screenWidth = GetSystemMetrics(SM_CXSCREEN);
        int screenHeight = GetSystemMetrics(SM_CYSCREEN);
        int x = (screenWidth - winWidth) / 2;
        int y = (screenHeight - winHeight) / 2;
        SetWindowPos(statusWindow, HWND_TOPMOST, x, y, 0, 0, SWP_NOSIZE);
        SetForegroundWindow(statusWindow);
        MessageBoxW(statusWindow, L"Client GUI started!", L"Debug", MB_OK | MB_ICONINFORMATION);
    }
    
    return statusWindow != nullptr;
}

// Status window procedure
LRESULT CALLBACK ClientGUI::StatusWindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    ClientGUI* gui = nullptr;
    
    if (msg == WM_NCCREATE) {
        CREATESTRUCT* cs = reinterpret_cast<CREATESTRUCT*>(lParam);
        gui = static_cast<ClientGUI*>(cs->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(gui));
    } else {
        gui = reinterpret_cast<ClientGUI*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }
    
    switch (msg) {
        case WM_PAINT: {
            if (gui) {
                PAINTSTRUCT ps;
                HDC hdc = BeginPaint(hwnd, &ps);
                gui->updateStatusWindow();
                EndPaint(hwnd, &ps);
            }
            return 0;
        }
        
        case WM_CLOSE:
            if (gui) {
                gui->showStatusWindow(false);
            }
            return 0;
            
        case WM_STATUS_UPDATE:
            if (gui) {
                InvalidateRect(hwnd, nullptr, TRUE);
            }
            return 0;
            
        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
}

// Tray window procedure (message-only window)
LRESULT CALLBACK ClientGUI::TrayWindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    ClientGUI* gui = reinterpret_cast<ClientGUI*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    
    switch (msg) {
        case WM_TRAYICON:
            if (gui && lParam == WM_RBUTTONUP) {
                POINT pt;
                GetCursorPos(&pt);
                gui->showContextMenu(pt);
            } else if (gui && lParam == WM_LBUTTONDBLCLK) {
                gui->toggleStatusWindow();
            }
            return 0;
            
        case WM_COMMAND:
            if (gui) {
                switch (LOWORD(wParam)) {
                    case ID_SHOW_STATUS:
                        gui->toggleStatusWindow();
                        break;
                    case ID_SHOW_CONSOLE:
                        gui->toggleConsoleWindow();
                        break;
                    case ID_EXIT:
                        PostQuitMessage(0);
                        break;
                }
            }
            return 0;
            
        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
}

// Show context menu
void ClientGUI::showContextMenu(POINT pt) {
    HMENU menu = CreatePopupMenu();
    if (!menu) return;
    
    AppendMenuW(menu, MF_STRING, ID_SHOW_STATUS, 
               statusWindowVisible ? L"Hide Status Window" : L"Show Status Window");
    AppendMenuW(menu, MF_STRING, ID_SHOW_CONSOLE, L"Toggle Console");
    AppendMenuW(menu, MF_SEPARATOR, 0, nullptr);
    AppendMenuW(menu, MF_STRING, ID_EXIT, L"Exit");
    
    // Make sure our window is in foreground to receive menu messages
    SetForegroundWindow(FindWindowW(TRAY_WINDOW_CLASS, nullptr));
    
    TrackPopupMenu(menu, TPM_RIGHTBUTTON, pt.x, pt.y, 0, 
                   FindWindowW(TRAY_WINDOW_CLASS, nullptr), nullptr);
    
    DestroyMenu(menu);
}

// Update status window content
void ClientGUI::updateStatusWindow() {
    if (!statusWindow) return;
    
    EnterCriticalSection(&statusLock);
    GUIStatus status = currentStatus; // Copy current status
    LeaveCriticalSection(&statusLock);
    
    HDC hdc = GetDC(statusWindow);
    if (!hdc) return;
    
    RECT rect;
    GetClientRect(statusWindow, &rect);
    
    // Clear background
    FillRect(hdc, &rect, (HBRUSH)(COLOR_WINDOW + 1));
    
    // Draw status information
    SetBkMode(hdc, TRANSPARENT);
    SetTextColor(hdc, RGB(0, 0, 0));
    
    int y = 10;
    int lineHeight = 20;
    
    // Connection status
    std::wstring connText = status.connected ? L"✓ Connected" : L"✗ Disconnected";
    SetTextColor(hdc, status.connected ? RGB(0, 128, 0) : RGB(255, 0, 0));
    TextOutW(hdc, 10, y, connText.c_str(), static_cast<int>(connText.length()));
    y += lineHeight;
    
    SetTextColor(hdc, RGB(0, 0, 0));
    
    // Phase
    std::wstring phaseText = L"Phase: " + std::wstring(status.phase.begin(), status.phase.end());
    TextOutW(hdc, 10, y, phaseText.c_str(), static_cast<int>(phaseText.length()));
    y += lineHeight;
    
    // Operation
    if (!status.operation.empty()) {
        std::wstring opText = L"Operation: " + std::wstring(status.operation.begin(), status.operation.end());
        TextOutW(hdc, 10, y, opText.c_str(), static_cast<int>(opText.length()));
        y += lineHeight;
    }
    
    // Progress
    if (status.totalProgress > 0) {
        std::wstring progText = L"Progress: " + std::to_wstring(status.progress) + 
                               L"/" + std::to_wstring(status.totalProgress) + 
                               L" (" + std::to_wstring((status.progress * 100) / status.totalProgress) + L"%)";
        TextOutW(hdc, 10, y, progText.c_str(), static_cast<int>(progText.length()));
        y += lineHeight;
        
        // Progress bar
        RECT progRect = {10, y, rect.right - 10, y + 15};
        FrameRect(hdc, &progRect, (HBRUSH)GetStockObject(BLACK_BRUSH));
        
        if (status.progress > 0) {
            RECT fillRect = progRect;
            fillRect.right = fillRect.left + ((fillRect.right - fillRect.left) * status.progress) / status.totalProgress;
            FillRect(hdc, &fillRect, (HBRUSH)GetStockObject(DKGRAY_BRUSH));
        }
        y += 25;
    }
    
    // Speed and ETA
    if (!status.speed.empty()) {
        std::wstring speedText = L"Speed: " + std::wstring(status.speed.begin(), status.speed.end());
        TextOutW(hdc, 10, y, speedText.c_str(), static_cast<int>(speedText.length()));
        y += lineHeight;
    }
    
    if (!status.eta.empty()) {
        std::wstring etaText = L"ETA: " + std::wstring(status.eta.begin(), status.eta.end());
        TextOutW(hdc, 10, y, etaText.c_str(), static_cast<int>(etaText.length()));
        y += lineHeight;
    }
    
    // Error
    if (!status.error.empty()) {
        SetTextColor(hdc, RGB(255, 0, 0));
        std::wstring errorText = L"Error: " + std::wstring(status.error.begin(), status.error.end());
        TextOutW(hdc, 10, y, errorText.c_str(), static_cast<int>(errorText.length()));
    }
    
    ReleaseDC(statusWindow, hdc);
}

// Update phase
void ClientGUI::updatePhase(const std::string& phase) {
    EnterCriticalSection(&statusLock);
    currentStatus.phase = phase;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
    
    // Update tray tooltip
    if (guiInitialized.load()) {
        std::wstring tooltip = L"Backup Client - " + std::wstring(phase.begin(), phase.end());
        wcsncpy_s(trayIcon.szTip, tooltip.c_str(), _TRUNCATE);
        Shell_NotifyIcon(NIM_MODIFY, &trayIcon);
    }
}

// Update operation
void ClientGUI::updateOperation(const std::string& operation, bool success, const std::string& details) {
    EnterCriticalSection(&statusLock);
    currentStatus.operation = operation;
    currentStatus.success = success;
    currentStatus.details = details;
    if (!success && !details.empty()) {
        currentStatus.error = details;
    } else if (success) {
        currentStatus.error.clear();
    }
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

// Update progress
void ClientGUI::updateProgress(int current, int total, const std::string& speed, const std::string& eta) {
    EnterCriticalSection(&statusLock);
    currentStatus.progress = current;
    currentStatus.totalProgress = total;
    currentStatus.speed = speed;
    currentStatus.eta = eta;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

// Update connection status
void ClientGUI::updateConnectionStatus(bool connected) {
    EnterCriticalSection(&statusLock);
    currentStatus.connected = connected;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
    
    // Update tray icon
    if (guiInitialized.load()) {
        // You could load different icons for connected/disconnected state here
        Shell_NotifyIcon(NIM_MODIFY, &trayIcon);
    }
}

// Update error
void ClientGUI::updateError(const std::string& error) {
    EnterCriticalSection(&statusLock);
    currentStatus.error = error;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

// Show notification
void ClientGUI::showNotification(const std::string& title, const std::string& message, DWORD iconType) {
    if (!guiInitialized.load()) return;
    
    std::wstring wTitle(title.begin(), title.end());
    std::wstring wMessage(message.begin(), message.end());
    
    wchar_t szInfoTitle[256];
    wcsncpy_s(szInfoTitle, wTitle.c_str(), _TRUNCATE);
    wcscpy_s(trayIcon.szInfoTitle, szInfoTitle);
    wchar_t szInfo[256];
    wcsncpy_s(szInfo, wMessage.c_str(), _TRUNCATE);
    wcscpy_s(trayIcon.szInfo, szInfo);
    trayIcon.dwInfoFlags = iconType;
    
    Shell_NotifyIcon(NIM_MODIFY, &trayIcon);
}

// Show popup
void ClientGUI::showPopup(const std::string& title, const std::string& message, UINT type) {
    std::wstring wTitle(title.begin(), title.end());
    std::wstring wMessage(message.begin(), message.end());
    
    MessageBoxW(nullptr, wMessage.c_str(), wTitle.c_str(), type);
}

// Toggle status window
void ClientGUI::toggleStatusWindow() {
    showStatusWindow(!statusWindowVisible);
}

// Show/hide status window
void ClientGUI::showStatusWindow(bool show) {
    if (!statusWindow) return;
    
    statusWindowVisible = show;
    ShowWindow(statusWindow, show ? SW_SHOW : SW_HIDE);
    
    if (show) {
        SetWindowPos(statusWindow, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
        InvalidateRect(statusWindow, nullptr, TRUE);
    }
}

// Toggle console window
void ClientGUI::toggleConsoleWindow() {
    if (consoleWindow) {
        bool visible = IsWindowVisible(consoleWindow) != FALSE;
        ShowWindow(consoleWindow, visible ? SW_HIDE : SW_SHOW);
    }
}

// Show/hide console window
void ClientGUI::showConsoleWindow(bool show) {
    if (consoleWindow) {
        ShowWindow(consoleWindow, show ? SW_SHOW : SW_HIDE);
    }
}

// Shutdown GUI
void ClientGUI::shutdown() {
    if (!guiInitialized.load()) return;
    
    shouldClose.store(true);
    
    // Post quit message to GUI thread
    PostQuitMessage(0);
    
    // Wait for GUI thread to finish
    if (guiThread.joinable()) {
        guiThread.join();
    }
    
    guiInitialized.store(false);
}

// Cleanup resources
void ClientGUI::cleanup() {
    if (trayIcon.hWnd) {
        Shell_NotifyIcon(NIM_DELETE, &trayIcon);
    }
    
    if (statusWindow) {
        DestroyWindow(statusWindow);
        statusWindow = nullptr;
    }
}

#endif // _WIN32

// --- ClientGUIHelpers stub implementations always available for linker ---
namespace ClientGUIHelpers {
    bool initializeGUI() { return true; }
    void shutdownGUI() {}
    void updatePhase(const std::string& phase) {}
    void updateOperation(const std::string& operation, bool success, const std::string& details) {}
    void updateProgress(int current, int total, const std::string& speed, const std::string& eta) {}
    void updateConnectionStatus(bool connected) {}
    void updateError(const std::string& message) {}
    void showNotification(const std::string& title, const std::string& message) {}
}
