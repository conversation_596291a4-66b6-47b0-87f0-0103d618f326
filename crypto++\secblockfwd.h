// secblockfwd.h - written and placed in the public domain by <PERSON>

/// \file secblockfwd.h
/// \brief Forward declarations for Sec<PERSON>lock
/// \details secblock.h and misc.h have a circular dependency. secblockfwd.h
///  allows the library to sidestep the circular dependency, and reference
///  Sec<PERSON>lock classes without the full implementation.
/// \since Crypto++ 8.3

#ifndef CRYPTOPP_SECBLOCKFWD_H
#define CRYPTOPP_SECBLOCKFWD_H

#include "config.h"

NAMESPACE_BEGIN(CryptoPP)

template <class T, class A>
class SecBlock;

template <class T, bool A>
class AllocatorWithCleanup;

typedef Sec<PERSON><PERSON><byte, AllocatorWithCleanup<byte, false> > Sec<PERSON><PERSON><PERSON><PERSON>;
typedef Se<PERSON><PERSON><PERSON><word, AllocatorWithCleanup<word, false> > SecWordBlock;
typedef Sec<PERSON><PERSON><byte, AllocatorWithCleanup<byte,  true> > AlignedSecByteBlock;

NAMESPACE_END

#endif  // CRYPTOPP_SECBLOCKFWD_H
